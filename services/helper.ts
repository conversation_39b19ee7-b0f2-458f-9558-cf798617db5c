import { Input } from "@/components/ui/input";
import { LivekitInputMessageType } from "@/shared/interfaces";

export function sanitizeString(text: string): string {
  if (!text) {
    return "";
  }

  // Replace underscores and hyphens with spaces
  let formattedText = text.replace(/[_-]/g, " ");

  // Convert to lowercase and then capitalize the first letter of each word
  formattedText = formattedText
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  return formattedText;
}
