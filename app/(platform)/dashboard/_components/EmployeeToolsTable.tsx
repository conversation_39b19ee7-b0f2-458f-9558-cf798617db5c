import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Plus,
  Minus,
  ChevronRight,
  Loader2,
  Package2,
  PackageIcon,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import React, { useState, useEffect } from "react";
import { sanitizeString } from "@/services/helper";
import { cn } from "@/lib/utils";
import { mcpApi } from "@/app/api/mcp";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { MCPInDB } from "@/shared/interfaces";
import { MCPCategory } from "@/shared/enums";

interface Tool extends MCPInDB {
  isAdded: boolean;
}

export const EmployeeToolsTable = ({
  setTools,
  initialToolIds = [],
}: {
  setTools: (tools: string[]) => void;
  initialToolIds?: string[];
}) => {
  const [activeCategory, setActiveCategory] = useState<string>("All");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [addedToolIds, setAddedToolIds] = useState<Set<string>>(
    new Set(initialToolIds)
  );

  const {
    data: mcpData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["mcps"],
    queryFn: () => mcpApi.getMcpServersByUser(),
  });

  useEffect(() => {
    setTools(Array.from(addedToolIds));
  }, [addedToolIds, setTools]);

  const handleAddTool = (toolId: string) => {
    setAddedToolIds((prevIds) => {
      const newIds = new Set(prevIds).add(toolId);
      return newIds;
    });
  };

  const handleRemoveTool = (toolId: string) => {
    setAddedToolIds((prevIds) => {
      const newIds = new Set(prevIds);
      newIds.delete(toolId);
      return newIds;
    });
  };

  const handleCategoryClick = (categoryKey: string) => {
    setActiveCategory(categoryKey);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const tools: Tool[] = React.useMemo(() => {
    if (!mcpData?.data) return [];
    // Handle both nested and direct array response
    const mcpArray = Array.isArray(mcpData.data)
      ? mcpData.data
      : mcpData.data?.data || [];
    return mcpArray.map((mcp) => ({
      ...mcp,
      isAdded: addedToolIds.has(mcp.id),
    }));
  }, [mcpData, addedToolIds]);

  const filteredTools = tools.filter((tool) => {
    const categoryMatch =
      activeCategory === "All" || tool.department === activeCategory;
    const searchMatch = tool.name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return categoryMatch && searchMatch;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-brand-primary" />
      </div>
    );
  }

  if (error || !mcpData) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-brand-secondary-font">
        <Package2 className="h-12 w-12 mb-4" />
        <p>Error fetching tools</p>
      </div>
    );
  }

  // Check for empty data in both nested and direct array response
  const mcpArray = Array.isArray(mcpData.data)
    ? mcpData.data
    : mcpData.data?.data || [];
  if (mcpArray.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-brand-secondary-font">
        <Package2 className="h-12 w-12 mb-4" />
        <p>No data found</p>
      </div>
    );
  }

  const truncateDescription = (description: string) => {
    return description.length > 50
      ? `${description.slice(0, 50)}...`
      : description;
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <div></div>
        <Input
          placeholder="Search tools by name..."
          className="w-full max-w-xs sm:max-w-sm md:max-w-md"
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </div>
      <CategoryRow
        activeCategory={activeCategory}
        onCategoryClick={handleCategoryClick}
      />
      <div className="rounded-lg border border-brand-stroke bg-brand-card shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b-brand-stroke">
              <TableHead className="w-[200px] px-6 py-4 text-brand-primary-font font-semibold">
                Tools
              </TableHead>
              <TableHead className="px-6 py-4 text-brand-primary-font font-semibold">
                Description
              </TableHead>
              <TableHead className="px-6 py-4 text-brand-primary-font font-semibold">
                Category
              </TableHead>
              <TableHead className="text-right px-6 py-4 text-brand-primary-font font-semibold">
                Add tool
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTools.map((tool) => {
              const isToolAdded = addedToolIds.has(tool.id);
              return (
                <TableRow
                  key={tool.id}
                  className={`border-b-brand-stroke ${
                    isToolAdded ? "bg-brand-card-hover" : ""
                  }`}
                >
                  <TableCell className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      {tool.logo ? (
                        <Image
                          src={tool.logo}
                          alt={tool.name}
                          width={20}
                          height={20}
                        />
                      ) : (
                        <PackageIcon className="w-4 h-4" />
                      )}
                      <span className="font-medium text-brand-primary-font">
                        {tool.name}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-4 text-brand-secondary-font">
                    {truncateDescription(tool.description)}
                  </TableCell>
                  <TableCell className="px-6 py-4">
                    <Badge
                      variant="outline"
                      className="border-brand-stroke bg-brand-background text-brand-secondary-font"
                    >
                      {sanitizeString(tool.department)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right px-6 py-4">
                    {isToolAdded ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-brand-primary text-brand-white-text hover:bg-brand-primary/90 border-brand-primary"
                        onClick={() => handleRemoveTool(tool.id)}
                      >
                        <Minus className="mr-2 h-4 w-4" />
                        Remove
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-brand-primary text-brand-primary hover:bg-brand-clicked"
                        onClick={() => handleAddTool(tool.id)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

interface CategoryRowProps {
  activeCategory: string;
  onCategoryClick: (categoryKey: string) => void;
}

const CategoryRow = ({ activeCategory, onCategoryClick }: CategoryRowProps) => {
  const displayCategories = ["All", ...Object.values(MCPCategory)];

  return (
    <div className="flex items-center w-full py-1">
      <div className="flex-grow overflow-x-auto whitespace-nowrap space-x-2 no-scrollbar">
        {displayCategories.map((category) => {
          const isActive = activeCategory === category;
          const isAllButton = category === "All";
          return (
            <Button
              key={category}
              variant="outline"
              className={cn(
                "rounded-lg h-9 align-middle text-sm flex-shrink-0",
                "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                isAllButton ? "px-5" : "px-3",
                isActive && isAllButton
                  ? "bg-brand-primary text-brand-white-text hover:bg-brand-primary/90 border-transparent"
                  : isActive
                  ? "bg-brand-primary/10 text-brand-primary border-brand-primary hover:bg-brand-primary/20"
                  : "bg-brand-input text-brand-secondary-font hover:bg-brand-clicked border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font dark:hover:bg-brand-primary/10 dark:border-brand-stroke"
              )}
              onClick={() => onCategoryClick(category)}
            >
              {sanitizeString(category)}
            </Button>
          );
        })}
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="flex-shrink-0 text-brand-secondary-font hover:text-brand-primary-font dark:text-brand-secondary-font dark:hover:text-brand-white-text ml-1"
      >
        <ChevronRight className="h-5 w-5" />
      </Button>
    </div>
  );
};
